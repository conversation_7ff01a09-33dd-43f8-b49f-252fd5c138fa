import request from '@/utils/request';
import { pref } from '@/utils/common';

// 获取时间设置
export const getTimeSetting = (data, code) => request.post(`${pref + code}/biz/registrationTime/list`, data)
// 获取民办时间设置
export const getTimePrivate = (data, code) => request.post(`${pref + code}/privatee/biz/time/detail`, data)
// 保存时间设置
export const saveTimeSetting = (data, code) => request.post(`${pref + code}/biz/registrationTime/create`, data)
// 获取民办时间设置
export const updateTimePrivate = (data, code) => request.post(`${pref + code}/privatee/biz/time/update`, data)
// 获取截止修改报名时间
export const getDeadline = (data, code) => request.post(`${pref + code}/biz/registrationTime/deadline`, data)

// 保存截止修改报名时间
export const deadlineCreate = (data, code) => request.post(`${pref + code}/biz/registrationTime/deadlineCreate`, data)
// 省市区列表
export const regionsList = params => request.get('/user-api/center/regions/tree', params)
// 学校 - 列表
export const schoolList = data => request.post('/user-api/center/dept/pageList', data)
// 学校列表
export const deptPageList = params => request.post('/user-api/center/dept/pageList', params)
// 学校 - 更新
export const uptSchool = data => request.post('/user-api/center/dept/updateSchool', data)
// 小区列表
export const xiaoquList = params => request.post('/user-api/center/schoolRang/xiaoquList', params)

// 学校 - 新增
export const addSchool = data => request.post('/user-api/center/dept/createSchool', data)

// 学校 - 删
export const delSchool = data => request.post('/user-api/center/dept/delete', data)

// 学校 - 启
export const enableSchool = data => request.post('/user-api/center/dept/changeStatusNormal', data)

// 学校 - 禁
export const disableSchool = data => request.post('/user-api/center/dept/changeStatusDisabled', data)

// 学校 - 详情
export const schoolDetail = data => request.post('/user-api/center/dept/detail', data)

// 学校 - 批量导入
export const importSchool = data => request.post('/user-api/center/dept/importSchool', data)
export const importSchools = data => request.post('/user-api/center/dept/importSchools', data)

export const editStudentCode = data => request.post('/user-api/center/studentCode/editStudentCode', data)


// 毕业小学学籍管理 - 列表
export const getList = data => request.post('/user-api/center/studentCode/pageList', data)

// 毕业小学学籍管理 - 批量导入
export const importStudentCode = data => request.post('/user-api/center/studentCode/importStudentCode', data)

// 学校关系  - 批量导入
export const importSchoolRelation = data => request.post('/user-api/center/dept/importSchoolRelation', data)

// 招生范围 - 列表
export const getSchoolRangeList = data => request.post('/user-api/center/schoolRang/pageList', data)

// 招生范围 - 添加
export const createSchoolRange = data => request.post('/user-api/center/schoolRang/createSchoolRang', data)

// 招生范围 - 修改
export const updateSchoolRange = data => request.post('/user-api/center/schoolRang/updateSchoolRang', data)

// 招生范围 - 删除
export const deleteSchoolRange = data => request.post('/user-api/center/schoolRang/delete', data)

// 招生范围 - 批量导入
export const importSchoolRange = data => request.post('/user-api/center/schoolRang/importSchoolRang', data)

// 报名类别详情设置 - 列表
export const signUpSortDetailSetting = (data, code) => request.post(`${pref + code}/biz/fieldConfig/signUpSortDetailSetting`, data)

// 报名类别详情设置 - 编辑
export const signUpSortDetailSettingUpdate = (data, code) => request.post(`${pref + code}/biz/fieldConfig/signUpSortDetailSettingUpdate`, data)

//市五区报名时间获取详情
export const getBaoMingShiJianDetail = (data, code) => request.post(`${pref + code}/five/enrollTime/detail`, data)

//市五区修改报名时间
export  const getBaoMingShiJianDetailUpdate = (data, code) => request.post(`${pref + code}/five/enrollTime/update`, data)

//涉县报名时间获取详情
export const getSheXianBaoMingShiJianDetail = (data, code) => request.post(`${pref + code}/biz/enrollTime/detail`, data)

//涉县修改报名时间
export  const getSheXianBaoMingShiJianDetailUpdate = (data, code) => request.post(`${pref + code}/biz/enrollTime/update`, data)
//获取市五区学校新增报名截止时间
export  const getBaoMingShiJianCheck = (data, code) => request.post(`${pref + code}/five/enrollTime/check`, data)

//获取非市五区学校新增报名截止时间
export  const getAddSignCheck = (data, code) => request.post(`${pref + code}/biz/registrationTime/verifyDeadline`, data)
export  const getPrivateAddSignCheck = (data, code) => request.post(`${pref + code}/privatee/biz/time/check`, data)
// 招生范围
export const getSchoolRange = data => request.post('/user-api/center/schoolRang/list', data)

// 省市区 code 转明文
export const getChildrenStr = data => request.post('/user-api/center/regions/childrenStr', data)

//涉县设置学校单独报名时间
export const  getTimeDetail=(data)=>request.post(`/user-api/center/schoolTime/detail`,data)
export const  saveSchoolTime=(data,code)=>request.post(`/user-api/center/schoolTime/saveSchoolTime`,data)
//涉县报名类别详情设置
export const getLeiBieList=(data,code)=>request.post(`${pref + code}/biz/signType/list`,data)
export const editLeiBie=(data,code)=>request.post(`${pref + code}/biz/signType/update`,data)
//涉县小学报名年龄设置
export const ageUpdatae=(data,code)=>request.post(`${pref + code}/biz/ageSet/update`,data)
export const ageDetail=(data,code)=>request.post(`${pref + code}/biz/ageSet/detail`,data)
//涉县学校关联
export const getGuanLianList=(data)=>request.post(`/user-api/center/schoolRelation/pageList`,data)
export const addGuanLian=(data)=>request.post("/user-api/center/schoolRelation/createSchoolRelation",data)
export const deleteJuniorGuanLian=(data)=>request.post("/user-api/center/schoolRelation/deleteJunior",data)
export const deletePrimaryGuanLian=(data)=>request.post("/user-api/center/schoolRelation/deletePrimary",data)
//小学对口初中 - 列表（峰峰矿区）- 暂存
// export const getSchoolRelation = data => request.post('/user-api/center/schoolRelation/list', data)
// 当前学校获取报名类别 和当前学校的报名详情
export const selectSchoolSetUpIds = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/selectSchoolSetUpIds`, data)
// 保存学校报名类型设置
export const saveSchoolEnrollType = (data, code) => request.post(`${pref + code}/biz/registrationSystemSetup/saveSchoolEnrollType`, data)
//查询报名类型层级结构


//小学对口初中 - 添加（峰峰矿区）- 暂存
// export const createSchoolRelation = data => request.post('/user-api/center/schoolRelation/createSchoolRelation', data)

//小学对口初中 - 删除小学（峰峰矿区）- 暂存
// export const deletePrimary = data => request.post('/user-api/center/schoolRelation/deletePrimary', data)

//小学对口初中 - 删除初中（峰峰矿区）- 暂存
// export const deleteJunior = data => request.post('/user-api/center/schoolRelation/deleteJunior', data)
//小学对口初中 - 删除
export const deleteSchoolRelation = data => request.post('/user-api/center/schoolRelation/deleteSchoolRelation', data)
// 功能开关
export const permissionList = (data, code) => request.post(`${pref + code}/biz/permission/list`, data)

// 功能开关更新
export const permissionSave = (data, code) => request.post(`${pref + code}/biz/permission/save`, data)

// 二次白名单 - 列表
export const getWhitelistList = (data, code) => request.post(`${pref + code}/biz/system/secondWhitelist/pageList`, data)

// 二次白名单 - 新增
export const addWhitelist = (data, code) => request.post(`${pref + code}/biz/system/secondWhitelist/create`, data)

// 二次白名单 - 编辑
export const updateWhitelist = (data, code) => request.post(`${pref + code}/biz/system/secondWhitelist/update`, data)

// 二次白名单 - 删除
export const deleteWhitelist = (data, code) => request.post(`${pref + code}/biz/system/secondWhitelist/delete`, data)

// 二次白名单 - 根据身份证号查询
export const getWhitelistByIdCard = (idCardNumber, code) => request.get(`${pref + code}/biz/system/secondWhitelist/getByIdCard`, { params: { idCardNumber } })

