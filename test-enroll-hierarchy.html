<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报名类型层级结构测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .school-info {
            background: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .hierarchy-table {
            margin-bottom: 30px;
        }
        
        .matched-types {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .tag-selection-container .el-tag {
            margin-right: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tag-selection-container .el-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h2>报名类型层级结构匹配测试</h2>
            
            <!-- 模拟学校信息 -->
            <div class="school-info">
                <h3>当前学校信息</h3>
                <p><strong>学校名称：</strong>{{ currentSchool.deptName }}</p>
                <p><strong>办学类型：</strong>{{ getSchoolTypeName(currentSchool.type) }}</p>
                <p><strong>学段：</strong>{{ getPeriodName(currentSchool.period) }}</p>
                <p><strong>性质：</strong>{{ currentSchool.nature }}</p>
                
                <el-button @click="changeSchool(1)" type="primary" size="small">切换到公办小学</el-button>
                <el-button @click="changeSchool(2)" type="success" size="small">切换到民办小学</el-button>
                <el-button @click="changeSchool(3)" type="warning" size="small">切换到公办幼儿园</el-button>
            </div>
            
            <!-- 层级结构数据表格 -->
            <div class="hierarchy-table">
                <h3>报名类型层级结构数据</h3>
                <el-table :data="enrollTypeHierarchyData" border size="small">
                    <el-table-column prop="entrance" label="入口" width="100"></el-table-column>
                    <el-table-column prop="schoolType" label="办学类型" width="100"></el-table-column>
                    <el-table-column prop="period" label="学段" width="100"></el-table-column>
                    <el-table-column prop="enrollCategory" label="报名类别"></el-table-column>
                </el-table>
            </div>
            
            <!-- 匹配结果 -->
            <div class="matched-types">
                <h3>匹配到的报名类型</h3>
                <div class="tag-selection-container">
                    <el-tag
                        v-for="item in enrollTypeAll"
                        :key="item.value"
                        :type="selectedTypes.includes(item.value) ? 'success' : 'info'"
                        :effect="selectedTypes.includes(item.value) ? 'dark' : 'plain'"
                        @click="toggleType(item.value)">
                        {{ item.value }} - {{ item.name }}
                    </el-tag>
                </div>
                <p v-if="enrollTypeAll.length === 0" style="color: #999;">暂无匹配的报名类型</p>
            </div>
            
            <!-- 已选择的类型 -->
            <div>
                <h3>已选择的报名类型</h3>
                <el-tag
                    v-for="typeCode in selectedTypes"
                    :key="typeCode"
                    type="success"
                    style="margin-right: 8px; margin-bottom: 8px;">
                    {{ getTypeName(typeCode) }}
                </el-tag>
                <p v-if="selectedTypes.length === 0" style="color: #999;">暂无已选择的报名类型</p>
                
                <p><strong>字符串格式：</strong>{{ selectedTypes.join(',') }}</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    currentSchool: {
                        id: 1,
                        deptName: '深南县第一实验小学',
                        type: 1, // 1-公办, 2-民办
                        period: 2, // 1-幼儿园, 2-小学, 3-初中
                        nature: 2
                    },
                    // 模拟层级结构数据
                    enrollTypeHierarchyData: [
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '第一批次:两统一' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '第二批次:户口房产不一致' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '第三批次:迁父母外来务工' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '第五批次:随迁及其他情况入学' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '优抚对象' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '第四批次:深南县房产无户籍' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '随迁子女(深南县房产)' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '幼儿园', enrollCategory: '其他' },
                        
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '第一批次:两统一' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '第二批次:户口房产不一致' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '第三批次:迁父母外来务工' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '第五批次:随迁及其他情况入学' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '优抚对象' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '第四批次:深南县房产无户籍' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '随迁子女(深南县房产)' },
                        { entrance: '乡镇报名', schoolType: '公办', period: '小学', enrollCategory: '其他' },
                        
                        { entrance: '乡镇报名', schoolType: '民办', period: '小学', enrollCategory: '民办第一批次' },
                        { entrance: '乡镇报名', schoolType: '民办', period: '小学', enrollCategory: '民办第二批次' },
                        { entrance: '乡镇报名', schoolType: '民办', period: '小学', enrollCategory: '民办第三批次' }
                    ],
                    enrollTypeAll: [],
                    selectedTypes: ['A', 'C']
                }
            },
            mounted() {
                this.matchEnrollTypesForSchool(this.currentSchool);
            },
            methods: {
                getSchoolTypeName(type) {
                    const map = { 1: '公办', 2: '民办' };
                    return map[type] || '未知';
                },
                getPeriodName(period) {
                    const map = { 1: '幼儿园', 2: '小学', 3: '初中' };
                    return map[period] || '未知';
                },
                changeSchool(type) {
                    const schools = {
                        1: { id: 1, deptName: '深南县第一实验小学', type: 1, period: 2, nature: 2 },
                        2: { id: 2, deptName: '深南县民办小学', type: 2, period: 2, nature: 2 },
                        3: { id: 3, deptName: '深南县第一幼儿园', type: 1, period: 1, nature: 2 }
                    };
                    this.currentSchool = schools[type];
                    this.selectedTypes = [];
                    this.matchEnrollTypesForSchool(this.currentSchool);
                },
                matchEnrollTypesForSchool(school) {
                    const schoolTypeMap = { 1: '公办', 2: '民办' };
                    const periodMap = { 1: '幼儿园', 2: '小学', 3: '初中' };
                    
                    const schoolType = schoolTypeMap[school.type] || '';
                    const period = periodMap[school.period] || '';
                    
                    console.log('匹配条件:', { schoolType, period });
                    
                    // 匹配对应的报名类别
                    const matchedCategories = this.enrollTypeHierarchyData.filter(item => {
                        return item.schoolType === schoolType && item.period === period;
                    });
                    
                    console.log('匹配结果:', matchedCategories);
                    
                    // 更新可用的报名类型
                    if (matchedCategories.length > 0) {
                        const uniqueCategories = [...new Set(matchedCategories.map(item => item.enrollCategory))];
                        this.enrollTypeAll = uniqueCategories.map((category, index) => ({
                            value: String.fromCharCode(65 + index), // A、B、C、D、E、F、G...
                            name: category
                        }));
                    } else {
                        this.enrollTypeAll = [];
                    }
                },
                toggleType(typeCode) {
                    const index = this.selectedTypes.indexOf(typeCode);
                    if (index > -1) {
                        this.selectedTypes.splice(index, 1);
                    } else {
                        this.selectedTypes.push(typeCode);
                    }
                },
                getTypeName(typeCode) {
                    const typeItem = this.enrollTypeAll.find(item => item.value === typeCode);
                    return typeItem ? `${typeCode} - ${typeItem.name}` : typeCode;
                }
            }
        });
    </script>
</body>
</html>
